# app/services/websocket/video_manager.py
import asyncio
import cv2
import logging
import time
import threading
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

from .config import config

logger = logging.getLogger(__name__)


class VideoQuality(Enum):
    """Video quality settings"""
    LOW = {"fps": 15, "scale": 0.5, "bitrate": 500}
    MEDIUM = {"fps": 20, "scale": 0.75, "bitrate": 1000}
    HIGH = {"fps": 30, "scale": 1.0, "bitrate": 2000}


class RecordingState(Enum):
    """Recording states"""
    INITIALIZING = "initializing"
    RECORDING = "recording"
    PAUSED = "paused"
    STOPPING = "stopping"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class VideoMetrics:
    """Video recording metrics"""
    frames_processed: int = 0
    frames_dropped: int = 0
    bytes_written: int = 0
    avg_frame_rate: float = 0.0
    peak_frame_rate: float = 0.0
    encoding_errors: int = 0
    last_quality_check: float = field(default_factory=time.time)

    def update_frame_rate(self, current_fps: float):
        """Update frame rate metrics"""
        self.avg_frame_rate = (self.avg_frame_rate + current_fps) / 2
        self.peak_frame_rate = max(self.peak_frame_rate, current_fps)


@dataclass
class VideoRecording:
    """Enhanced video recording with better state management"""
    session_id: str
    video_path: Path
    quality: VideoQuality = VideoQuality.MEDIUM
    video_writer: Optional[cv2.VideoWriter] = None
    frame_count: int = 0
    start_time: float = field(default_factory=time.time)
    last_frame_time: float = field(default_factory=time.time)
    consecutive_errors: int = 0
    state: RecordingState = RecordingState.INITIALIZING
    metrics: VideoMetrics = field(default_factory=VideoMetrics)
    frame_buffer: List[np.ndarray] = field(default_factory=list)
    max_buffer_size: int = 30  # Buffer up to 1 second at 30fps

    def update_frame_time(self):
        self.last_frame_time = time.time()

    def increment_error_count(self):
        """Increment consecutive error count"""
        self.consecutive_errors += 1
        self.metrics.encoding_errors += 1

    def reset_error_count(self):
        """Reset consecutive error count"""
        self.consecutive_errors = 0

    def get_duration(self) -> float:
        """Get recording duration in seconds"""
        return time.time() - self.start_time

    def is_expired(self) -> bool:
        """Check if recording has expired due to inactivity"""
        return (time.time() - self.last_frame_time) > config.websocket.video_frame_timeout

    def has_too_many_errors(self) -> bool:
        """Check if recording has too many consecutive errors"""
        return self.consecutive_errors >= config.websocket.max_consecutive_errors

    def is_active(self) -> bool:
        """Check if recording is in an active state"""
        return self.state in [RecordingState.RECORDING, RecordingState.PAUSED]

    def add_to_buffer(self, frame: np.ndarray) -> bool:
        """Add frame to buffer"""
        if len(self.frame_buffer) >= self.max_buffer_size:
            # Remove oldest frame
            self.frame_buffer.pop(0)
            self.metrics.frames_dropped += 1

        self.frame_buffer.append(frame)
        return True

    def get_buffered_frames(self) -> List[np.ndarray]:
        """Get and clear buffered frames"""
        frames = self.frame_buffer.copy()
        self.frame_buffer.clear()
        return frames


class VideoProcessor:
    """Handles video frame processing and optimization"""

    def __init__(self):
        self.frame_processors: Dict[str, Callable] = {}
        self._register_default_processors()

    def _register_default_processors(self):
        """Register default frame processors"""
        self.frame_processors["resize"] = self._resize_frame
        self.frame_processors["denoise"] = self._denoise_frame
        self.frame_processors["stabilize"] = self._stabilize_frame

    def _resize_frame(self, frame: np.ndarray, scale: float) -> np.ndarray:
        """Resize frame based on quality settings"""
        if scale != 1.0:
            height, width = frame.shape[:2]
            new_width = int(width * scale)
            new_height = int(height * scale)
            return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
        return frame

    def _denoise_frame(self, frame: np.ndarray) -> np.ndarray:
        """Apply denoising to frame"""
        return cv2.fastNlMeansDenoisingColored(frame, None, 10, 10, 7, 21)

    def _stabilize_frame(self, frame: np.ndarray) -> np.ndarray:
        """Basic frame stabilization (placeholder)"""
        # This would implement video stabilization algorithms
        return frame

    def process_frame(self, frame: np.ndarray, quality: VideoQuality, processors: List[str] = None) -> np.ndarray:
        """Process frame with specified processors"""
        processed_frame = frame.copy()

        # Apply quality-based resizing
        scale = quality.value["scale"]
        processed_frame = self._resize_frame(processed_frame, scale)

        # Apply additional processors if specified
        if processors:
            for processor_name in processors:
                if processor_name in self.frame_processors:
                    try:
                        processed_frame = self.frame_processors[processor_name](processed_frame)
                    except Exception as e:
                        logger.warning(f"Frame processor {processor_name} failed: {e}")

        return processed_frame


class VideoManager:
    """Enhanced video recording and processing manager"""

    def __init__(self):
        self.recordings: Dict[str, VideoRecording] = {}
        self.processor = VideoProcessor()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._processing_tasks: Dict[str, asyncio.Task] = {}
        self._lock = threading.Lock()
        self._start_cleanup_task()

    def _start_cleanup_task(self):
        """Start periodic cleanup of expired recordings"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """Periodically clean up expired recordings and optimize performance"""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                await self._cleanup_expired_recordings()
                await self._optimize_active_recordings()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in video cleanup: {e}")

    async def _cleanup_expired_recordings(self):
        """Clean up expired recordings"""
        expired_sessions = []

        for session_id, recording in self.recordings.items():
            if recording.is_expired() or recording.has_too_many_errors():
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            logger.info(f"Cleaning up expired video recording: {session_id}")
            await self.stop_recording(session_id)

    async def _optimize_active_recordings(self):
        """Optimize active recordings based on performance metrics"""
        for recording in self.recordings.values():
            if recording.is_active():
                await self._adjust_quality_if_needed(recording)
    
    async def _adjust_quality_if_needed(self, recording: VideoRecording):
        """Adjust video quality based on performance metrics"""
        try:
            current_time = time.time()
            if current_time - recording.metrics.last_quality_check < 10:  # Check every 10 seconds
                return

            recording.metrics.last_quality_check = current_time

            # Check if we need to reduce quality due to performance issues
            if recording.metrics.frames_dropped > 10 and recording.quality != VideoQuality.LOW:
                logger.info(f"Reducing video quality for session {recording.session_id} due to dropped frames")
                recording.quality = VideoQuality.LOW
                recording.metrics.frames_dropped = 0

            # Check if we can increase quality
            elif recording.metrics.frames_dropped == 0 and recording.quality == VideoQuality.LOW:
                logger.info(f"Increasing video quality for session {recording.session_id}")
                recording.quality = VideoQuality.MEDIUM

        except Exception as e:
            logger.error(f"Error adjusting quality for {recording.session_id}: {e}")

    def _get_video_storage_path(self, session_id: str, quality: VideoQuality) -> Optional[Path]:
        """Get video storage path for session with quality indicator"""
        storage_path = config.get_video_storage_path()
        if not storage_path:
            logger.error("No available video storage path")
            return None

        timestamp = int(time.time() * 1000)
        quality_suffix = quality.name.lower()
        filename = f"interview_{session_id}_{timestamp}_{quality_suffix}.mp4"
        return storage_path / filename

    def _initialize_video_writer(
        self,
        video_path: Path,
        frame_width: int,
        frame_height: int,
        quality: VideoQuality
    ) -> Optional[cv2.VideoWriter]:
        """Initialize video writer with quality settings"""
        try:
            # Use H.264 codec for better compression and compatibility
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = quality.value["fps"]

            video_writer = cv2.VideoWriter(
                str(video_path),
                fourcc,
                fps,
                (frame_width, frame_height)
            )

            if not video_writer.isOpened():
                raise Exception("Failed to open video writer")
            return video_writer

        except Exception as e:
            logger.error(f"Failed to initialize video writer: {e}")
            return None

    async def _process_buffered_frames(self, recording: VideoRecording) -> bool:
        """Process buffered frames asynchronously"""
        try:
            frames = recording.get_buffered_frames()
            if not frames:
                return True

            for frame in frames:
                if not recording.video_writer or not recording.video_writer.isOpened():
                    logger.warning(f"Video writer not available for {recording.session_id}")
                    return False

                recording.video_writer.write(frame)
                recording.frame_count += 1
                recording.metrics.frames_processed += 1
                recording.metrics.bytes_written += frame.nbytes

            return True

        except Exception as e:
            logger.error(f"Error processing buffered frames for {recording.session_id}: {e}")
            return False
    
    async def start_recording(self, session_id: str, quality: VideoQuality = VideoQuality.MEDIUM) -> bool:
        """Start video recording for session with specified quality"""
        try:
            if session_id in self.recordings:
                logger.warning(f"Recording already active for session {session_id}")
                return True

            video_path = self._get_video_storage_path(session_id, quality)
            if not video_path:
                return False

            recording = VideoRecording(
                session_id=session_id,
                video_path=video_path,
                quality=quality,
                state=RecordingState.INITIALIZING
            )

            self.recordings[session_id] = recording

            # Start background processing task
            self._processing_tasks[session_id] = asyncio.create_task(
                self._background_frame_processor(session_id)
            )

            recording.state = RecordingState.RECORDING
            logger.info(f"Started video recording for session {session_id} with {quality.name} quality: {video_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to start recording for {session_id}: {e}")
            return False

    async def _background_frame_processor(self, session_id: str):
        """Background task to process buffered frames"""
        try:
            while session_id in self.recordings:
                recording = self.recordings[session_id]

                if recording.state == RecordingState.STOPPING:
                    break

                if recording.frame_buffer:
                    await self._process_buffered_frames(recording)

                await asyncio.sleep(0.1)  # Process every 100ms

        except asyncio.CancelledError:
            logger.debug(f"Background processor cancelled for {session_id}")
        except Exception as e:
            logger.error(f"Error in background processor for {session_id}: {e}")

    async def pause_recording(self, session_id: str) -> bool:
        """Pause video recording"""
        try:
            recording = self.recordings.get(session_id)
            if not recording:
                return False

            recording.state = RecordingState.PAUSED
            logger.info(f"Paused video recording for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to pause recording for {session_id}: {e}")
            return False

    async def resume_recording(self, session_id: str) -> bool:
        """Resume video recording"""
        try:
            recording = self.recordings.get(session_id)
            if not recording or recording.state != RecordingState.PAUSED:
                return False

            recording.state = RecordingState.RECORDING
            logger.info(f"Resumed video recording for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to resume recording for {session_id}: {e}")
            return False
    
    async def process_frame(self, session_id: str, frame) -> bool:
        """Process a video frame with enhanced quality control and buffering"""
        try:
            recording = self.recordings.get(session_id)
            if not recording or not recording.is_active():
                logger.warning(f"No active recording for session {session_id}")
                return False

            # Skip processing if paused
            if recording.state == RecordingState.PAUSED:
                return True

            # Check if recording has exceeded maximum duration
            if recording.get_duration() > config.websocket.max_video_duration:
                logger.info(f"Maximum recording time reached for session {session_id}")
                await self.stop_recording(session_id)
                return False

            # Convert frame to numpy array
            img = frame.to_ndarray(format="bgr24")
            if img is None or img.size == 0:
                logger.warning(f"Invalid frame data for session {session_id}")
                recording.increment_error_count()

                if recording.has_too_many_errors():
                    logger.error(f"Too many invalid frames for session {session_id}")
                    await self.stop_recording(session_id)
                    return False
                return True

            # Process frame with quality settings
            processed_frame = self.processor.process_frame(img, recording.quality)

            # Initialize video writer if needed
            if recording.video_writer is None:
                frame_height, frame_width = processed_frame.shape[:2]
                recording.video_writer = self._initialize_video_writer(
                    recording.video_path, frame_width, frame_height, recording.quality
                )

                if recording.video_writer is None:
                    logger.error(f"Failed to initialize video writer for {session_id}")
                    await self.stop_recording(session_id)
                    return False

            # Add frame to buffer for background processing
            success = recording.add_to_buffer(processed_frame)
            if success:
                recording.update_frame_time()
                recording.reset_error_count()

                # Update metrics
                if recording.frame_count > 0:
                    fps = recording.frame_count / recording.get_duration()
                    recording.metrics.update_frame_rate(fps)

                return True
            else:
                logger.warning(f"Failed to buffer frame for session {session_id}")
                return False

        except Exception as e:
            logger.error(f"Error processing frame for session {session_id}: {e}")
            recording = self.recordings.get(session_id)
            if recording:
                recording.increment_error_count()
                if recording.has_too_many_errors():
                    await self.stop_recording(session_id)
            return False
    
    async def stop_recording(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Stop video recording and return recording info with enhanced cleanup"""
        try:
            recording = self.recordings.get(session_id)
            if not recording:
                logger.warning(f"No recording found for session {session_id}")
                return None

            # Set state to stopping
            recording.state = RecordingState.STOPPING

            # Cancel background processing task
            if session_id in self._processing_tasks:
                task = self._processing_tasks[session_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self._processing_tasks[session_id]

            # Process any remaining buffered frames
            if recording.frame_buffer:
                await self._process_buffered_frames(recording)

            # Release video writer
            if recording.video_writer:
                try:
                    recording.video_writer.release()
                    logger.info(f"Video writer released for session {session_id}")
                except Exception as e:
                    logger.error(f"Error releasing video writer: {e}")

            # Calculate final stats
            duration = recording.get_duration()
            recording.state = RecordingState.COMPLETED

            # Create comprehensive recording info
            recording_info = {
                "session_id": session_id,
                "video_path": str(recording.video_path),
                "frame_count": recording.frame_count,
                "duration": duration,
                "video_url": f"/candidates/{recording.video_path.name}",
                "completed_at": datetime.now(timezone.utc).isoformat(),
                "quality": recording.quality.name,
                "metrics": {
                    "frames_processed": recording.metrics.frames_processed,
                    "frames_dropped": recording.metrics.frames_dropped,
                    "avg_frame_rate": recording.metrics.avg_frame_rate,
                    "peak_frame_rate": recording.metrics.peak_frame_rate,
                    "encoding_errors": recording.metrics.encoding_errors,
                    "bytes_written": recording.metrics.bytes_written
                }
            }

            del self.recordings[session_id]
            return recording_info
        except Exception as e:
            logger.error(f"Error stopping recording for session {session_id}: {e}")
            # Clean up anyway
            self.recordings.pop(session_id, None)
            self._processing_tasks.pop(session_id, None)
            return None
    
    def is_recording(self, session_id: str) -> bool:
        """Check if session is currently recording"""
        recording = self.recordings.get(session_id)
        return recording is not None and recording.is_active()

    def get_recording_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive information about current recording"""
        recording = self.recordings.get(session_id)
        if not recording:
            return None

        return {
            "session_id": session_id,
            "frame_count": recording.frame_count,
            "duration": recording.get_duration(),
            "state": recording.state.value,
            "quality": recording.quality.name,
            "consecutive_errors": recording.consecutive_errors,
            "last_frame_time": recording.last_frame_time,
            "video_path": str(recording.video_path),
            "buffer_size": len(recording.frame_buffer),
            "metrics": {
                "frames_processed": recording.metrics.frames_processed,
                "frames_dropped": recording.metrics.frames_dropped,
                "avg_frame_rate": recording.metrics.avg_frame_rate,
                "peak_frame_rate": recording.metrics.peak_frame_rate,
                "encoding_errors": recording.metrics.encoding_errors,
                "bytes_written": recording.metrics.bytes_written
            }
        }

    async def set_recording_quality(self, session_id: str, quality: VideoQuality) -> bool:
        """Change recording quality for active session"""
        try:
            recording = self.recordings.get(session_id)
            if not recording or not recording.is_active():
                return False

            old_quality = recording.quality
            recording.quality = quality

            logger.info(f"Changed video quality for session {session_id} from {old_quality.name} to {quality.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set quality for {session_id}: {e}")
            return False

    async def get_recording_metrics(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed metrics for a recording session"""
        recording = self.recordings.get(session_id)
        if not recording:
            return None

        return {
            "session_id": session_id,
            "duration": recording.get_duration(),
            "frame_count": recording.frame_count,
            "quality": recording.quality.name,
            "state": recording.state.value,
            "performance": {
                "frames_processed": recording.metrics.frames_processed,
                "frames_dropped": recording.metrics.frames_dropped,
                "drop_rate": recording.metrics.frames_dropped / max(recording.metrics.frames_processed, 1),
                "avg_frame_rate": recording.metrics.avg_frame_rate,
                "peak_frame_rate": recording.metrics.peak_frame_rate,
                "encoding_errors": recording.metrics.encoding_errors,
                "bytes_written": recording.metrics.bytes_written,
                "buffer_utilization": len(recording.frame_buffer) / recording.max_buffer_size
            }
        }

    async def cleanup_all(self):
        """Clean up all recordings and background tasks"""
        session_ids = list(self.recordings.keys())
        for session_id in session_ids:
            await self.stop_recording(session_id)

        # Cancel all background tasks
        for task in self._processing_tasks.values():
            if not task.done():
                task.cancel()
        self._processing_tasks.clear()

        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive video manager statistics"""
        total_frames = sum(r.frame_count for r in self.recordings.values())
        total_errors = sum(r.metrics.encoding_errors for r in self.recordings.values())
        total_dropped = sum(r.metrics.frames_dropped for r in self.recordings.values())

        return {
            "active_recordings": len(self.recordings),
            "background_tasks": len(self._processing_tasks),
            "total_frames_processed": total_frames,
            "total_encoding_errors": total_errors,
            "total_frames_dropped": total_dropped,
            "recordings": {
                session_id: {
                    "frame_count": recording.frame_count,
                    "duration": recording.get_duration(),
                    "state": recording.state.value,
                    "quality": recording.quality.name,
                    "consecutive_errors": recording.consecutive_errors,
                    "buffer_size": len(recording.frame_buffer),
                    "avg_fps": recording.metrics.avg_frame_rate
                }
                for session_id, recording in self.recordings.items()
            }
        }
