from app.core.logging_config import log_performance
from app.core.error_handling import error_handler, <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorContext, ErrorSeverity
from app.services.websocket.webrtc_manager import WebRTCManager
from app.services.websocket.video_manager import VideoManager
from app.services.websocket.config import config
import asyncio
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)

class ProcessingState(Enum):
    ACTIVE = "active"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class ProcessingStats:
    frame_count: int = 0
    consecutive_errors: int = 0
    start_time: float = 0
    last_frame_time: float = 0
    
    def reset_errors(self):
        self.consecutive_errors = 0
    
    def increment_error(self):
        self.consecutive_errors += 1
    
    def increment_frame(self):
        self.frame_count += 1
        self.last_frame_time = time.time()

class VideoHandler:
    def __init__(self):
        self.webrtc_manager = WebRTCManager()
        self.video_manager = VideoManager()
        self.processing_sessions: Dict[str, ProcessingStats] = {}
        
        # Configuration constants
        self.MAX_CONSECUTIVE_ERRORS = config.websocket.max_consecutive_errors
        self.FRAME_TIMEOUT = config.websocket.video_frame_timeout
        self.RETRY_DELAY = config.websocket.retry_delay
        self.CONNECTION_CHECK_INTERVAL = config.websocket.connection_check_interval

    def _create_error_context(self, session_id: str, operation: str = "video_track_handling") -> ErrorContext:
        """Create standardized error context"""
        return ErrorContext(
            session_id=session_id,
            operation=operation,
            component="video_handler"
        )

    def _is_connection_active(self, session_id: str) -> bool:
        """Check if WebRTC connection is active"""
        if not self.webrtc_manager.is_processing_video(session_id):
            return False
            
        connection = self.webrtc_manager.get_connection(session_id)
        return connection and connection.state not in ["failed", "closed"]

    def _classify_error(self, error_message: str) -> tuple[ErrorCode, ErrorSeverity, bool]:
        """Classify error and determine if it's recoverable"""
        error_lower = error_message.lower()
        
        # Media stream errors - usually recoverable
        if "mediastreamError" in error_message or "connection" in error_lower:
            return ErrorCode.VIDEO_FRAME_INVALID, ErrorSeverity.LOW, True
            
        # Codec errors - medium severity, might be recoverable
        elif "codec" in error_lower or "encoding" in error_lower:
            return ErrorCode.VIDEO_CODEC_ERROR, ErrorSeverity.MEDIUM, True
            
        # Empty errors - usually connection issues
        elif not error_message.strip():
            return ErrorCode.VIDEO_FRAME_INVALID, ErrorSeverity.LOW, True
            
        # Unknown errors - treat as non-recoverable
        else:
            return ErrorCode.VIDEO_FRAME_INVALID, ErrorSeverity.MEDIUM, False

    async def _handle_frame_error(self, session_id: str, error: Exception, stats: ProcessingStats) -> bool:
        """Handle frame processing errors with smart retry logic"""
        error_message = str(error) if str(error) else f"{type(error).__name__} (no message)"
        error_code, severity, is_recoverable = self._classify_error(error_message)
        
        stats.increment_error()
        context = self._create_error_context(session_id, "frame_processing")
        
        # Log based on error frequency
        if stats.consecutive_errors <= 3:
            logger.warning(f"Frame error for session {session_id} (attempt {stats.consecutive_errors}): {error_message}")
        else:
            logger.error(f"Repeated frame error for session {session_id} (attempt {stats.consecutive_errors}): {error_message}")
        
        # Create error record for tracking
        error_handler.create_error(
            code=error_code,
            message=f"Frame processing error for session {session_id}: {error_message}",
            context=context,
            exception=error,
            severity=severity
        )
        
        # Determine if we should continue processing
        if not is_recoverable or stats.consecutive_errors >= self.MAX_CONSECUTIVE_ERRORS:
            return False
            
        # Smart retry delay based on error type and count
        retry_delay = min(self.RETRY_DELAY * (stats.consecutive_errors ** 0.5), 2.0)
        await asyncio.sleep(retry_delay)
        return True

    async def _initialize_recording(self, session_id: str) -> bool:
        """Initialize video recording with proper error handling"""
        context = self._create_error_context(session_id, "recording_initialization")
        
        try:
            if not await self.video_manager.start_recording(session_id):
                error_handler.create_error(
                    code=ErrorCode.VIDEO_RECORDING_FAILED,
                    message=f"Failed to start video recording for session {session_id}",
                    context=context,
                    severity=ErrorSeverity.HIGH,
                    recovery_suggestions=[
                        "Check video storage permissions",
                        "Verify disk space availability",
                        "Restart video recording service"
                    ]
                )
                return False
            
            # Initialize session stats
            self.processing_sessions[session_id] = ProcessingStats(start_time=time.time())
            logger.info(f"Video recording initialized for session {session_id}")
            return True
            
        except Exception as e:
            error_handler.create_error(
                code=ErrorCode.VIDEO_RECORDING_FAILED,
                message=f"Exception during recording initialization for session {session_id}: {str(e)}",
                context=context,
                exception=e,
                severity=ErrorSeverity.HIGH
            )
            return False

    async def _finalize_recording(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Finalize video recording with cleanup"""
        context = self._create_error_context(session_id, "recording_finalization")
        stats = self.processing_sessions.get(session_id)
        
        try:
            recording_info = await self.video_manager.stop_recording(session_id)
            
            if recording_info and stats:
                duration = time.time() - stats.start_time
                recording_info.update({
                    'frames_processed': stats.frame_count,
                    'duration_seconds': duration,
                    'avg_fps': stats.frame_count / duration if duration > 0 else 0
                })
                
                return recording_info
            else:
                error_handler.create_error(
                    code=ErrorCode.VIDEO_RECORDING_FAILED,
                    message=f"Failed to stop video recording properly for session {session_id}",
                    context=context,
                    severity=ErrorSeverity.MEDIUM
                )
                return None
                
        except Exception as e:
            error_handler.create_error(
                code=ErrorCode.VIDEO_RECORDING_FAILED,
                message=f"Exception during recording finalization for session {session_id}: {str(e)}",
                context=context,
                exception=e,
                severity=ErrorSeverity.MEDIUM
            )
            return None
        finally:
            # Clean up session stats
            self.processing_sessions.pop(session_id, None)

    async def _process_video_frames(self, track, session_id: str) -> ProcessingState:
        """Main video frame processing loop with optimized error handling"""
        stats = self.processing_sessions[session_id]
        last_connection_check = time.time()
        
        while True:
            # Periodic connection check (avoid checking every frame)
            current_time = time.time()
            if current_time - last_connection_check > self.CONNECTION_CHECK_INTERVAL:
                if not self._is_connection_active(session_id):
                    return ProcessingState.STOPPED
                last_connection_check = current_time
            
            try:
                # Receive frame with timeout
                frame = await asyncio.wait_for(track.recv(), timeout=self.FRAME_TIMEOUT)
                
                if frame is None:
                    logger.info(f"Received None frame for session {session_id}, ending video processing")
                    return ProcessingState.STOPPED
                
                # Process frame
                success = await self.video_manager.process_frame(session_id, frame)
                
                if success:
                    stats.increment_frame()
                    stats.reset_errors()
                else:
                    stats.increment_error()
                    if stats.consecutive_errors >= self.MAX_CONSECUTIVE_ERRORS:
                        logger.error(f"Too many consecutive frame processing failures for session {session_id}")
                        return ProcessingState.ERROR
                
            except asyncio.TimeoutError:
                # Timeout is expected, continue processing
                logger.debug(f"Frame timeout for session {session_id}")
                continue
                
            except Exception as e:
                should_continue = await self._handle_frame_error(session_id, e, stats)
                if not should_continue:
                    return ProcessingState.ERROR

    @log_performance("video_track_handling", "video")
    async def handle_video_track(self, track, session_id: str) -> Optional[Dict[str, Any]]:
        """Handle incoming video track from WebRTC with comprehensive error handling"""
        
        try:
            # Initialize recording
            if not await self._initialize_recording(session_id):
                return None
            
            # Process video frames
            processing_state = await self._process_video_frames(track, session_id)
            
            # Log final state
            self.processing_sessions.get(session_id)
            # Finalize recording
            return await self._finalize_recording(session_id)
            
        except Exception as e:
            context = self._create_error_context(session_id)
            error_handler.create_error(
                code=ErrorCode.VIDEO_RECORDING_FAILED,
                message=f"Unexpected video track handling error for session {session_id}: {str(e)}",
                context=context,
                exception=e,
                severity=ErrorSeverity.HIGH,
                recovery_suggestions=[
                    "Restart video recording service",
                    "Check WebRTC connection status",
                    "Verify video codec support"
                ]
            )
            logger.error(f"Video track handling error for session {session_id}: {e}")
            return None
            
        finally:
            # Cleanup
            try:
                self.webrtc_manager.stop_video_processing(session_id)
                self.processing_sessions.pop(session_id, None)
                logger.debug(f"Video processing cleanup completed for session {session_id}")
            except Exception as cleanup_error:
                logger.error(f"Error during video processing cleanup for session {session_id}: {cleanup_error}")

    async def get_processing_stats(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get current processing statistics for a session"""
        stats = self.processing_sessions.get(session_id)
        if not stats:
            return None
            
        current_time = time.time()
        duration = current_time - stats.start_time
        
        return {
            'session_id': session_id,
            'frame_count': stats.frame_count,
            'consecutive_errors': stats.consecutive_errors,
            'duration_seconds': duration,
            'avg_fps': stats.frame_count / duration if duration > 0 else 0,
            'last_frame_ago': current_time - stats.last_frame_time if stats.last_frame_time else None
        }

    async def stop_processing(self, session_id: str) -> bool:
        """Gracefully stop video processing for a session"""
        try:
            self.webrtc_manager.stop_video_processing(session_id)
            await self._finalize_recording(session_id)
            logger.info(f"Video processing stopped for session {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error stopping video processing for session {session_id}: {e}")
            return False